a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:60787:"a:1:{s:8:"messages";a:54:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.022545;i:4;a:0:{}i:5;i:2616216;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.026159;i:4;a:0:{}i:5;i:2733224;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.028014;i:4;a:0:{}i:5;i:2774432;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.028037;i:4;a:0:{}i:5;i:2774808;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748757321.054487;i:4;a:0:{}i:5;i:3920008;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1748757321.070295;i:4;a:0:{}i:5;i:4730496;}i:6;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1748757321.092875;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6035128;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.14531;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172544;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.176589;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6282864;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.184147;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6577632;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.197813;i:4;a:0:{}i:5;i:7268000;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748757321.20151;i:4;a:0:{}i:5;i:8041224;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748757321.202026;i:4;a:0:{}i:5;i:8066056;}i:45;a:6:{i:0;s:49:"Route requested: 'backend/material-return/accept'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1748757321.202989;i:4;a:0:{}i:5;i:8123448;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748757321.203;i:4;a:0:{}i:5;i:8125080;}i:47;a:6:{i:0;s:44:"Route to run: backend/material-return/accept";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1748757321.204586;i:4;a:0:{}i:5;i:8221424;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.208989;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8702128;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.213175;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8708744;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1748757321.217163;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8712120;}i:55;a:6:{i:0;s:88:"Running action: app\modules\backend\controllers\MaterialReturnController::actionAccept()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1748757321.217203;i:4;a:0:{}i:5;i:8711288;}i:56;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1748757321.218918;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:223;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8781736;}i:57;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.219795;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8822880;}i:60;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.226265;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8832888;}i:63;a:6:{i:0;s:99:"SELECT * FROM "material_status_group" WHERE ("id"='49') AND ("status"=4) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.230349;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8839680;}i:66;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='49') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.235079;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8875384;}i:69;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.237674;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8887272;}i:72;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.242729;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8896352;}i:75;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.246559;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8931080;}i:78;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.253632;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8940312;}i:81;a:6:{i:0;s:83:"SELECT * FROM "material_storage" WHERE ("material_id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.25791;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8946048;}i:84;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.267293;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9168736;}i:87;a:6:{i:0;s:58:"UPDATE "material_storage" SET "quantity"='5' WHERE "id"=12";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.271069;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9166104;}i:90;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.2751;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9208480;}i:93;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.281848;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9219408;}i:96;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.286873;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9241344;}i:99;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=12)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.288099;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9246744;}i:102;a:6:{i:0;s:195:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "add_user_id", "type", "created_at") VALUES (12, 1, '10.000', 1, 2, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.289248;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9249584;}i:105;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.294164;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9278000;}i:108;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.29946;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9287424;}i:111;a:6:{i:0;s:54:"SELECT * FROM "supplier_balance" WHERE "supplier_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.303996;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9294144;}i:114;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.309089;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9350376;}i:117;a:6:{i:0;s:95:"UPDATE "supplier_balance" SET "amount"='-1335', "updated_at"='2025-06-01 10:55:21' WHERE "id"=4";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.311932;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9349968;}i:120;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.313441;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9381728;}i:123;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.319688;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9392528;}i:126;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.323908;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9411688;}i:129;a:6:{i:0;s:171:"INSERT INTO "supplier_balance_history" ("supplier_id", "amount", "old_amount", "type", "created_at") VALUES (1, '-1335', '165.00', 4, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.32523;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9419256;}i:132;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=6)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.329377;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9425144;}i:135;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.330735;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9430552;}i:138;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.332194;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9435912;}i:141;a:6:{i:0;s:106:"UPDATE "material_status_group" SET "accepted_user_id"=1, "accepted_at"='2025-06-01 10:55:21' WHERE "id"=49";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.333427;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9439336;}i:144;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.336083;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9452624;}i:147;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.343438;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9463416;}i:150;a:6:{i:0;s:315:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (1, 'accept_material_return_with_prices', 'material_status_group', '"\"49\""'::jsonb, '"{\"materials_count\":1,\"total_amount\":1500,\"material_prices\":{\"1\":\"150\"}}"'::jsonb, '2025-06-01 10:55:21')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.350127;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9509456;}i:153;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1748757321.354376;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:331;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9509112;}}}";s:9:"profiling";s:114202:"a:3:{s:6:"memory";i:9605992;s:4:"time";d:0.3546910285949707;s:8:"messages";a:76:{i:7;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1748757321.092916;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6036256;}i:8;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1748757321.144008;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6038184;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.145359;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6174120;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.175174;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6189480;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.176626;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6284352;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.180194;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6285912;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.184199;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6578976;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.188931;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6581984;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.209018;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8704736;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.21253;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8706896;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.213202;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8711352;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.216435;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8713448;}i:58;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.219847;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8824744;}i:59;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.225603;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8840368;}i:61;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.226299;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8834752;}i:62;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.229707;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8838768;}i:64;a:6:{i:0;s:99:"SELECT * FROM "material_status_group" WHERE ("id"='49') AND ("status"=4) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.230389;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8842768;}i:65;a:6:{i:0;s:99:"SELECT * FROM "material_status_group" WHERE ("id"='49') AND ("status"=4) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.233761;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8850944;}i:67;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='49') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.235125;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8878416;}i:68;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='49') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.237267;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8881208;}i:70;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.237717;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8889136;}i:71;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.242185;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8901768;}i:73;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.242755;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8898216;}i:74;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.245655;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8901640;}i:76;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.246592;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8932944;}i:77;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.252889;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8945680;}i:79;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.253669;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8942176;}i:80;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.257229;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8945008;}i:82;a:6:{i:0;s:83:"SELECT * FROM "material_storage" WHERE ("material_id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.257943;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8949080;}i:83;a:6:{i:0;s:83:"SELECT * FROM "material_storage" WHERE ("material_id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.261693;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8951544;}i:85;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.267346;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9171736;}i:86;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.270422;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9173344;}i:88;a:6:{i:0;s:58:"UPDATE "material_storage" SET "quantity"='5' WHERE "id"=12";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.271104;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9169128;}i:89;a:6:{i:0;s:58:"UPDATE "material_storage" SET "quantity"='5' WHERE "id"=12";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.273955;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9170760;}i:91;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.27515;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9210344;}i:92;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.281194;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9228000;}i:94;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.281878;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9221272;}i:95;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.285931;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9225912;}i:97;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.286902;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9244344;}i:98;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.287613;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9245952;}i:100;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=12)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.288125;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9249760;}i:101;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=12)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.28881;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9251400;}i:103;a:6:{i:0;s:195:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "add_user_id", "type", "created_at") VALUES (12, 1, '10.000', 1, 2, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.289263;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9250072;}i:104;a:6:{i:0;s:195:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "add_user_id", "type", "created_at") VALUES (12, 1, '10.000', 1, 2, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.293207;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9252288;}i:106;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.294218;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9280240;}i:107;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.298926;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9290144;}i:109;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.299489;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9289664;}i:110;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.303494;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9292872;}i:112;a:6:{i:0;s:54:"SELECT * FROM "supplier_balance" WHERE "supplier_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.304023;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9296880;}i:113;a:6:{i:0;s:54:"SELECT * FROM "supplier_balance" WHERE "supplier_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.307919;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9299512;}i:115;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.309118;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9353752;}i:116;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.31149;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9355736;}i:118;a:6:{i:0;s:95:"UPDATE "supplier_balance" SET "amount"='-1335', "updated_at"='2025-06-01 10:55:21' WHERE "id"=4";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.311959;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9352768;}i:119;a:6:{i:0;s:95:"UPDATE "supplier_balance" SET "amount"='-1335', "updated_at"='2025-06-01 10:55:21' WHERE "id"=4";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.312741;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9354784;}i:121;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.313479;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9383968;}i:122;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.31915;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9398584;}i:124;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.319724;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9394768;}i:125;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.323146;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9397984;}i:127;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.323937;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9415064;}i:128;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.324763;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9417048;}i:130;a:6:{i:0;s:171:"INSERT INTO "supplier_balance_history" ("supplier_id", "amount", "old_amount", "type", "created_at") VALUES (1, '-1335', '165.00', 4, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.325245;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9420760;}i:131;a:6:{i:0;s:171:"INSERT INTO "supplier_balance_history" ("supplier_id", "amount", "old_amount", "type", "created_at") VALUES (1, '-1335', '165.00', 4, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.328385;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9423280;}i:133;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=6)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.329413;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9428144;}i:134;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=6)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.330329;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9429752;}i:136;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.330764;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9433552;}i:137;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.331611;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9435160;}i:139;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.332228;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9438912;}i:140;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.332943;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9440520;}i:142;a:6:{i:0;s:106:"UPDATE "material_status_group" SET "accepted_user_id"=1, "accepted_at"='2025-06-01 10:55:21' WHERE "id"=49";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.333457;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9442416;}i:143;a:6:{i:0;s:106:"UPDATE "material_status_group" SET "accepted_user_id"=1, "accepted_at"='2025-06-01 10:55:21' WHERE "id"=49";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.335074;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9444088;}i:145;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.336141;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9454864;}i:146;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.342694;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9469344;}i:148;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.3435;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9465656;}i:149;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.347908;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9468840;}i:151;a:6:{i:0;s:315:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (1, 'accept_material_return_with_prices', 'material_status_group', '"\"49\""'::jsonb, '"{\"materials_count\":1,\"total_amount\":1500,\"material_prices\":{\"1\":\"150\"}}"'::jsonb, '2025-06-01 10:55:21')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.350165;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9513080;}i:152;a:6:{i:0;s:315:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (1, 'accept_material_return_with_prices', 'material_status_group', '"\"49\""'::jsonb, '"{\"materials_count\":1,\"total_amount\":1500,\"material_prices\":{\"1\":\"150\"}}"'::jsonb, '2025-06-01 10:55:21')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.353972;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9515216;}}}";s:2:"db";s:113383:"a:1:{s:8:"messages";a:74:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.145359;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6174120;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.175174;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6189480;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.176626;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6284352;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.180194;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6285912;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.184199;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6578976;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.188931;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6581984;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.209018;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8704736;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.21253;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8706896;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.213202;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8711352;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.216435;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8713448;}i:58;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.219847;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8824744;}i:59;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.225603;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8840368;}i:61;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.226299;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8834752;}i:62;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.229707;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8838768;}i:64;a:6:{i:0;s:99:"SELECT * FROM "material_status_group" WHERE ("id"='49') AND ("status"=4) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.230389;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8842768;}i:65;a:6:{i:0;s:99:"SELECT * FROM "material_status_group" WHERE ("id"='49') AND ("status"=4) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.233761;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:227;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8850944;}i:67;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='49') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.235125;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8878416;}i:68;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='49') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.237267;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8881208;}i:70;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.237717;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8889136;}i:71;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.242185;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8901768;}i:73;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.242755;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8898216;}i:74;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.245655;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:247;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8901640;}i:76;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.246592;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8932944;}i:77;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.252889;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8945680;}i:79;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.253669;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8942176;}i:80;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.257229;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8945008;}i:82;a:6:{i:0;s:83:"SELECT * FROM "material_storage" WHERE ("material_id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.257943;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8949080;}i:83;a:6:{i:0;s:83:"SELECT * FROM "material_storage" WHERE ("material_id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.261693;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:269;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:8951544;}i:85;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.267346;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9171736;}i:86;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.270422;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9173344;}i:88;a:6:{i:0;s:58:"UPDATE "material_storage" SET "quantity"='5' WHERE "id"=12";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.271104;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9169128;}i:89;a:6:{i:0;s:58:"UPDATE "material_storage" SET "quantity"='5' WHERE "id"=12";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.273955;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:285;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9170760;}i:91;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.27515;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9210344;}i:92;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.281194;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9228000;}i:94;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.281878;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9221272;}i:95;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.285931;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9225912;}i:97;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.286902;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9244344;}i:98;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.287613;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9245952;}i:100;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=12)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.288125;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9249760;}i:101;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=12)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.28881;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9251400;}i:103;a:6:{i:0;s:195:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "add_user_id", "type", "created_at") VALUES (12, 1, '10.000', 1, 2, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.289263;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9250072;}i:104;a:6:{i:0;s:195:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "add_user_id", "type", "created_at") VALUES (12, 1, '10.000', 1, 2, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.293207;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:298;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9252288;}i:106;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.294218;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9280240;}i:107;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.298926;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9290144;}i:109;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.299489;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9289664;}i:110;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.303494;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9292872;}i:112;a:6:{i:0;s:54:"SELECT * FROM "supplier_balance" WHERE "supplier_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.304023;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9296880;}i:113;a:6:{i:0;s:54:"SELECT * FROM "supplier_balance" WHERE "supplier_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.307919;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:184;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9299512;}i:115;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.309118;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9353752;}i:116;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.31149;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9355736;}i:118;a:6:{i:0;s:95:"UPDATE "supplier_balance" SET "amount"='-1335', "updated_at"='2025-06-01 10:55:21' WHERE "id"=4";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.311959;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9352768;}i:119;a:6:{i:0;s:95:"UPDATE "supplier_balance" SET "amount"='-1335', "updated_at"='2025-06-01 10:55:21' WHERE "id"=4";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.312741;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:197;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9354784;}i:121;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.313479;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9383968;}i:122;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'supplier_balance_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.31915;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9398584;}i:124;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.319724;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9394768;}i:125;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='supplier_balance_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.323146;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:203;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9397984;}i:127;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.323937;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9415064;}i:128;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.324763;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9417048;}i:130;a:6:{i:0;s:171:"INSERT INTO "supplier_balance_history" ("supplier_id", "amount", "old_amount", "type", "created_at") VALUES (1, '-1335', '165.00', 4, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.325245;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9420760;}i:131;a:6:{i:0;s:171:"INSERT INTO "supplier_balance_history" ("supplier_id", "amount", "old_amount", "type", "created_at") VALUES (1, '-1335', '165.00', 4, '2025-06-01 10:55:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.328385;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:209;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:308;s:8:"function";s:21:"updateSupplierBalance";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9423280;}i:133;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=6)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.329413;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9428144;}i:134;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=6)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.330329;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9429752;}i:136;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.330764;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9433552;}i:137;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.331611;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9435160;}i:139;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.332228;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9438912;}i:140;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.332943;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9440520;}i:142;a:6:{i:0;s:106:"UPDATE "material_status_group" SET "accepted_user_id"=1, "accepted_at"='2025-06-01 10:55:21' WHERE "id"=49";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.333457;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9442416;}i:143;a:6:{i:0;s:106:"UPDATE "material_status_group" SET "accepted_user_id"=1, "accepted_at"='2025-06-01 10:55:21' WHERE "id"=49";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.335074;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:315;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9444088;}i:145;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.336141;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9454864;}i:146;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.342694;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9469344;}i:148;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.3435;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9465656;}i:149;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748757321.347908;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9468840;}i:151;a:6:{i:0;s:315:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (1, 'accept_material_return_with_prices', 'material_status_group', '"\"49\""'::jsonb, '"{\"materials_count\":1,\"total_amount\":1500,\"material_prices\":{\"1\":\"150\"}}"'::jsonb, '2025-06-01 10:55:21')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.350165;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9513080;}i:152;a:6:{i:0;s:315:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (1, 'accept_material_return_with_prices', 'material_status_group', '"\"49\""'::jsonb, '"{\"materials_count\":1,\"total_amount\":1500,\"material_prices\":{\"1\":\"150\"}}"'::jsonb, '2025-06-01 10:55:21')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748757321.353972;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialReturnAcceptService.php";s:4:"line";i:327;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:87:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\MaterialReturnController.php";s:4:"line";i:145;s:8:"function";s:30:"acceptMaterialReturnWithPrices";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialReturnAcceptService";s:4:"type";s:2:"->";}}i:5;i:9515216;}}}";s:5:"event";s:13859:"a:75:{i:0;a:5:{s:4:"time";d:1748757321.086718;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1748757321.143994;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1748757321.189364;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1748757321.189425;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1748757321.195318;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1748757321.20234;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1748757321.204702;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1748757321.204712;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1748757321.206895;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1748757321.206916;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1748757321.206927;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1748757321.206934;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1748757321.20694;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1748757321.206946;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1748757321.206952;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1748757321.206987;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:56:"app\modules\backend\controllers\MaterialReturnController";}i:16;a:5:{s:4:"time";d:1748757321.218972;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:17;a:5:{s:4:"time";d:1748757321.219676;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1748757321.234176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:19;a:5:{s:4:"time";d:1748757321.234245;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:20;a:5:{s:4:"time";d:1748757321.234905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:1748757321.237589;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:22;a:5:{s:4:"time";d:1748757321.246051;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:23;a:5:{s:4:"time";d:1748757321.24649;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1748757321.262108;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:25;a:5:{s:4:"time";d:1748757321.26216;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:26;a:5:{s:4:"time";d:1748757321.262559;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:27;a:5:{s:4:"time";d:1748757321.266239;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:1748757321.266361;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:1748757321.270876;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:30;a:5:{s:4:"time";d:1748757321.270902;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:31;a:5:{s:4:"time";d:1748757321.274641;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:32;a:5:{s:4:"time";d:1748757321.275014;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:33;a:5:{s:4:"time";d:1748757321.286527;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:34;a:5:{s:4:"time";d:1748757321.286753;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:1748757321.286785;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:1748757321.28792;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1748757321.288012;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:1748757321.289111;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:39;a:5:{s:4:"time";d:1748757321.289127;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:40;a:5:{s:4:"time";d:1748757321.293532;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:41;a:5:{s:4:"time";d:1748757321.294037;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:1748757321.308218;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:43;a:5:{s:4:"time";d:1748757321.308256;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:44;a:5:{s:4:"time";d:1748757321.30829;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:45;a:5:{s:4:"time";d:1748757321.30891;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:1748757321.308991;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:1748757321.3118;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:48;a:5:{s:4:"time";d:1748757321.311819;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:49;a:5:{s:4:"time";d:1748757321.31303;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\SupplierBalance";}i:50;a:5:{s:4:"time";d:1748757321.313378;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\SupplierBalanceHistory";}i:51;a:5:{s:4:"time";d:1748757321.323595;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\SupplierBalanceHistory";}i:52;a:5:{s:4:"time";d:1748757321.323786;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:53;a:5:{s:4:"time";d:1748757321.323818;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:54;a:5:{s:4:"time";d:1748757321.325075;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\SupplierBalanceHistory";}i:55;a:5:{s:4:"time";d:1748757321.325093;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\SupplierBalanceHistory";}i:56;a:5:{s:4:"time";d:1748757321.328829;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\SupplierBalanceHistory";}i:57;a:5:{s:4:"time";d:1748757321.328908;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:58;a:5:{s:4:"time";d:1748757321.329134;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:1748757321.329266;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:60;a:5:{s:4:"time";d:1748757321.330601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:1748757321.330638;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:62;a:5:{s:4:"time";d:1748757321.331986;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:63;a:5:{s:4:"time";d:1748757321.332025;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:1748757321.333287;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:65;a:5:{s:4:"time";d:1748757321.333309;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:66;a:5:{s:4:"time";d:1748757321.335324;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:67;a:5:{s:4:"time";d:1748757321.356552;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:68;a:5:{s:4:"time";d:1748757321.356628;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:56:"app\modules\backend\controllers\MaterialReturnController";}i:69;a:5:{s:4:"time";d:1748757321.35664;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:70;a:5:{s:4:"time";d:1748757321.356649;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:71;a:5:{s:4:"time";d:1748757321.356662;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:72;a:5:{s:4:"time";d:1748757321.35667;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:73;a:5:{s:4:"time";d:1748757321.358755;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:74;a:5:{s:4:"time";d:1748757321.358878;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1748757321.007726;s:3:"end";d:1748757321.363666;s:6:"memory";i:9711648;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4877:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.20283;i:4;a:0:{}i:5;i:8105136;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202848;i:4;a:0:{}i:5;i:8105888;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202856;i:4;a:0:{}i:5;i:8106640;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202862;i:4;a:0:{}i:5;i:8107392;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202867;i:4;a:0:{}i:5;i:8108144;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202873;i:4;a:0:{}i:5;i:8108896;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202878;i:4;a:0:{}i:5;i:8109648;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202884;i:4;a:0:{}i:5;i:8110400;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.20289;i:4;a:0:{}i:5;i:8111152;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202896;i:4;a:0:{}i:5;i:8111904;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202902;i:4;a:0:{}i:5;i:8112656;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202907;i:4;a:0:{}i:5;i:8113408;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202912;i:4;a:0:{}i:5;i:8115440;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202916;i:4;a:0:{}i:5;i:8116192;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202921;i:4;a:0:{}i:5;i:8116944;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202926;i:4;a:0:{}i:5;i:8117696;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202933;i:4;a:0:{}i:5;i:8118448;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202938;i:4;a:0:{}i:5;i:8119200;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202943;i:4;a:0:{}i:5;i:8119952;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202948;i:4;a:0:{}i:5;i:8120704;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202953;i:4;a:0:{}i:5;i:8121456;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.202957;i:4;a:0:{}i:5;i:8122208;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1748757321.202975;i:4;a:0:{}i:5;i:8124280;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748757321.20298;i:4;a:0:{}i:5;i:8124120;}}s:5:"route";s:30:"backend/material-return/accept";s:6:"action";s:72:"app\modules\backend\controllers\MaterialReturnController::actionAccept()";}";s:7:"request";s:6654:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:13:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"137";s:12:"x-csrf-token";s:88:"COWdZRG9nyXoY9UDrw9_4gt2Hb797wWDIzLBB8FliyBhgsoQIs7ecY8NpUvKaEyGbAd4i6SFbuJnfbJ_mTLIeQ==";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"origin";s:13:"http://silver";s:7:"referer";s:43:"http://silver/backend/material-return/index";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=rrpedd4d2o06m1los9h3ofh3qhec5rd6; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=0f9c3e1a7f7f74e0be35e6bce55fc297ef8304bba1f4e3808d96a7950b64fa64a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22igWu3sATgnpHeg3dgqe5YjkaDOsxXWCY%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683beb49306df";s:16:"X-Debug-Duration";s:3:"352";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683beb49306df";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Tue, 01-Jul-2025 05:55:21 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:30:"backend/material-return/accept";s:6:"action";s:72:"app\modules\backend\controllers\MaterialReturnController::actionAccept()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:1;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:137:"group_id=49&material_prices%5B1%5D=150&_csrf=COWdZRG9nyXoY9UDrw9_4gt2Hb797wWDIzLBB8FliyBhgsoQIs7ecY8NpUvKaEyGbAd4i6SFbuJnfbJ_mTLIeQ%3D%3D";s:7:"Decoded";a:3:{s:8:"group_id";s:2:"49";s:15:"material_prices";a:1:{i:1;s:3:"150";}s:5:"_csrf";s:88:"COWdZRG9nyXoY9UDrw9_4gt2Hb797wWDIzLBB8FliyBhgsoQIs7ecY8NpUvKaEyGbAd4i6SFbuJnfbJ_mTLIeQ==";}}s:6:"SERVER";a:42:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"137";s:17:"HTTP_X_CSRF_TOKEN";s:88:"COWdZRG9nyXoY9UDrw9_4gt2Hb797wWDIzLBB8FliyBhgsoQIs7ecY8NpUvKaEyGbAd4i6SFbuJnfbJ_mTLIeQ==";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ORIGIN";s:13:"http://silver";s:12:"HTTP_REFERER";s:43:"http://silver/backend/material-return/index";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=rrpedd4d2o06m1los9h3ofh3qhec5rd6; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=0f9c3e1a7f7f74e0be35e6bce55fc297ef8304bba1f4e3808d96a7950b64fa64a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22igWu3sATgnpHeg3dgqe5YjkaDOsxXWCY%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"53585";s:12:"REDIRECT_URL";s:31:"/backend/material-return/accept";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:31:"/backend/material-return/accept";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.989501;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:3:{s:8:"group_id";s:2:"49";s:15:"material_prices";a:1:{i:1;s:3:"150";}s:5:"_csrf";s:88:"COWdZRG9nyXoY9UDrw9_4gt2Hb797wWDIzLBB8FliyBhgsoQIs7ecY8NpUvKaEyGbAd4i6SFbuJnfbJ_mTLIeQ==";}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"rrpedd4d2o06m1los9h3ofh3qhec5rd6";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"0f9c3e1a7f7f74e0be35e6bce55fc297ef8304bba1f4e3808d96a7950b64fa64a:2:{i:0;s:5:"_csrf";i:1;s:32:"igWu3sATgnpHeg3dgqe5YjkaDOsxXWCY";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1748764521;s:13:"last_activity";i:1748757321;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'hDlF38UoPMOH4Koq5kFA5mtUZQW1FIcC2x1ZShA3'";s:8:"password";s:62:"'$2y$13$4TIVTzSuUrDvbyA/XTxgYeLOUkjr1YmfDSmKDjzI.OxKnUvJRzfk2'";s:10:"created_at";s:21:"'2025-02-24 16:46:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683beb49306df";s:3:"url";s:44:"http://silver/backend/material-return/accept";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.989501;s:10:"statusCode";i:200;s:8:"sqlCount";i:37;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9605992;s:14:"processingTime";d:0.3546910285949707;}s:10:"exceptions";a:0:{}}